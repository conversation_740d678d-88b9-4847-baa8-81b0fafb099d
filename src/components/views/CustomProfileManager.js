import { LitElement, html, css } from 'lit';

export class CustomProfileManager extends LitElement {
    static styles = css`
        :host {
            display: block;
            width: 100%;
        }

        .profile-manager {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .profile-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-bottom: 20px;
        }

        .profile-item {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 8px;
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .profile-info {
            flex: 1;
        }

        .profile-name {
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 4px;
        }

        .profile-description {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
        }

        .profile-actions {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #4f46e5;
            color: white;
        }

        .btn-primary:hover {
            background: #4338ca;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .btn-danger {
            background: #dc2626;
            color: white;
        }

        .btn-danger:hover {
            background: #b91c1c;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: rgba(255, 255, 255, 0.6);
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.6);
        }

        .error {
            background: rgba(220, 38, 38, 0.1);
            border: 1px solid rgba(220, 38, 38, 0.3);
            border-radius: 8px;
            padding: 12px;
            color: #fca5a5;
            margin-bottom: 16px;
        }
    `;

    static properties = {
        customProfiles: { type: Array },
        loading: { type: Boolean },
        error: { type: String },
        onCreateProfile: { type: Function },
        onEditProfile: { type: Function },
        onDeleteProfile: { type: Function },
    };

    constructor() {
        super();
        this.customProfiles = [];
        this.loading = false;
        this.error = '';
        this.onCreateProfile = () => {};
        this.onEditProfile = () => {};
        this.onDeleteProfile = () => {};
    }

    async connectedCallback() {
        super.connectedCallback();
        await this.loadCustomProfiles();
    }

    async loadCustomProfiles() {
        this.loading = true;
        this.error = '';
        
        try {
            if (window.cheddar && window.cheddar.getAllCustomProfiles) {
                this.customProfiles = await window.cheddar.getAllCustomProfiles();
            } else {
                this.error = 'Custom profile functions not available';
            }
        } catch (error) {
            console.error('Error loading custom profiles:', error);
            this.error = 'Failed to load custom profiles';
        } finally {
            this.loading = false;
        }
    }

    handleCreateProfile() {
        this.onCreateProfile();
    }

    handleEditProfile(profile) {
        this.onEditProfile(profile);
    }

    async handleDeleteProfile(profile) {
        if (confirm(`Are you sure you want to delete the profile "${profile.name}"? This action cannot be undone.`)) {
            try {
                if (window.cheddar && window.cheddar.deleteCustomProfile) {
                    await window.cheddar.deleteCustomProfile(profile.id);
                    await this.loadCustomProfiles(); // Reload the list
                    this.onDeleteProfile(profile);
                }
            } catch (error) {
                console.error('Error deleting profile:', error);
                this.error = 'Failed to delete profile';
            }
        }
    }

    formatDate(timestamp) {
        return new Date(timestamp).toLocaleDateString();
    }

    render() {
        return html`
            <div class="profile-manager">
                <div class="section-title">
                    <span>Custom Profiles</span>
                </div>

                ${this.error ? html`
                    <div class="error">
                        ${this.error}
                    </div>
                ` : ''}

                <div style="margin-bottom: 16px;">
                    <button class="btn btn-primary" @click=${this.handleCreateProfile}>
                        + Create New Profile
                    </button>
                </div>

                ${this.loading ? html`
                    <div class="loading">
                        Loading custom profiles...
                    </div>
                ` : this.customProfiles.length === 0 ? html`
                    <div class="empty-state">
                        <div class="empty-state-icon">📝</div>
                        <div>No custom profiles yet</div>
                        <div style="margin-top: 8px; font-size: 14px;">
                            Create your first custom profile to get started
                        </div>
                    </div>
                ` : html`
                    <div class="profile-list">
                        ${this.customProfiles.map(profile => html`
                            <div class="profile-item">
                                <div class="profile-info">
                                    <div class="profile-name">${profile.name}</div>
                                    <div class="profile-description">
                                        ${profile.description || 'No description'}
                                        • Created ${this.formatDate(profile.created)}
                                    </div>
                                </div>
                                <div class="profile-actions">
                                    <button 
                                        class="btn btn-secondary" 
                                        @click=${() => this.handleEditProfile(profile)}
                                    >
                                        Edit
                                    </button>
                                    <button 
                                        class="btn btn-danger" 
                                        @click=${() => this.handleDeleteProfile(profile)}
                                    >
                                        Delete
                                    </button>
                                </div>
                            </div>
                        `)}
                    </div>
                `}
            </div>
        `;
    }
}

customElements.define('custom-profile-manager', CustomProfileManager);

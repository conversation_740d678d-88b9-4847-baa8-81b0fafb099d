{"name": "cheating-daddy", "productName": "cheating-daddy", "version": "0.4.0", "description": "cheating daddy", "main": "src/index.js", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "echo \"No linting configured\""}, "keywords": ["cheating daddy", "cheating daddy ai", "cheating daddy ai assistant", "cheating daddy ai assistant for interviews", "cheating daddy ai assistant for interviews"], "author": {"name": "sohzm", "email": "<EMAIL>"}, "license": "GPL-3.0", "dependencies": {"@google/genai": "^1.2.0", "electron-squirrel-startup": "^1.0.1"}, "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-dmg": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "@electron-forge/plugin-fuses": "^7.8.1", "@electron/fuses": "^1.8.0", "@reforged/maker-appimage": "^5.0.0", "electron": "^30.0.5"}}